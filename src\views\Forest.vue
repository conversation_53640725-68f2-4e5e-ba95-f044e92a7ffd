<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTheme } from '../store/theme';
import { useI18n } from 'vue-i18n';
import { useForest } from '../store/forest';
import { useWeather } from '../store/weather';
import { useLocation } from '../store/location';
import { useBitmapRequest } from '../composables/useBitmapRequest';

// Router and theme
const router = useRouter();
const { isDarkMode } = useTheme();
const { t } = useI18n();

// Get forest state
const {
  currentStage,
  co2Absorbed,
  co2Target,
  totalCO2Mitigated,
  totalItemsRecycled,
  totalTreesGrown,
  progressPercentage,
} = useForest();

// Get location state
const {
  currentPosition,
  initializeLocation,
} = useLocation();

// Get weather state
const {
  weatherData,
  weatherEmoji,
  weatherDescription,
  temperature,
  isLoading: weatherLoading,
  initializeWeather,
} = useWeather();

// Weather status
const isWeatherLoaded = ref(false);

// Bitmap request for forest data
const { fetchData: fetchForestData, isLoading: forestLoading, error: forestError } = useBitmapRequest();

// Forest data state
const forestApiData = ref(null);
const lastFetchTime = ref(null);

// Component version
const version = '1.0.0';

// Tree images based on progress percentage
const treeNoLeafImage = new URL('../assets/tree-no-leaf.svg', import.meta.url).href;
const treeSmallImage = new URL('../assets/tree-small.svg', import.meta.url).href;
const treeMediumImage = new URL('../assets/tree-medium.svg', import.meta.url).href;
const treeBigImage = new URL('../assets/tree-big.svg', import.meta.url).href;

// Get tree image based on progress percentage
const getTreeImageByProgress = (percentage) => {
  if (percentage <= 25) {
    return treeNoLeafImage;
  } else if (percentage <= 50) {
    return treeSmallImage;
  } else if (percentage <= 75) {
    return treeMediumImage;
  } else {
    return treeBigImage;
  }
};

// Determine which trees to show based on stage and progress
const treesToShow = computed(() => {
  const trees = [];
  const currentProgress = progressPercentage.value;

  // Stage 1: Only show 1 tree based on progress
  if (currentStage.value === 1) {
    trees.push({
      image: getTreeImageByProgress(currentProgress),
      position: 'center',
    });
  }
  // Stage 2: Show 1 big tree and 1 tree based on progress
  else if (currentStage.value === 2) {
    trees.push({
      image: treeBigImage,
      position: 'left',
    });
    trees.push({
      image: getTreeImageByProgress(currentProgress),
      position: 'right',
    });
  }
  // Stage 3: Show 2 big trees and 1 tree based on progress
  else if (currentStage.value === 3) {
    trees.push({
      image: treeBigImage,
      position: 'left',
    });
    trees.push({
      image: treeBigImage,
      position: 'center',
    });
    trees.push({
      image: getTreeImageByProgress(currentProgress),
      position: 'right',
    });
  }
  // Stage 4+: Show 3 big trees and 1 tree based on progress
  else {
    trees.push({
      image: treeBigImage,
      position: 'far-left',
    });
    trees.push({
      image: treeBigImage,
      position: 'left',
    });
    trees.push({
      image: treeBigImage,
      position: 'center',
    });
    trees.push({
      image: getTreeImageByProgress(currentProgress),
      position: 'right',
    });
  }

  return trees;
});

// Cloud images
const cloudImage = new URL('../assets/cloud.svg', import.meta.url).href;
const smallCloudImage = new URL('../assets/small-cloud.svg', import.meta.url).href;


// Fetch forest data from API
const fetchForestStats = async () => {
  try {
    console.log('Fetching forest statistics from API...');

    // Use bitmap request to fetch forest data - using bitmap 1024 with function "1"
    const response = await fetchForestData(1024, '1');

    if (response && response.data && response.data.length > 0) {
      forestApiData.value = response;
      lastFetchTime.value = new Date();

      // Check if we have Table0 with campaign data
      const responseData = response.data[0];
      if (responseData && responseData.Table0 && responseData.Table0.length > 0) {
        const campaignData = responseData.Table0[0];

        console.log('Campaign data received:', campaignData);

        // Map campaign data to forest stats
        // For now, we'll use the campaign data to derive forest information
        if (campaignData.CampaignId !== undefined) {
          // Use campaign ID to determine stage (example mapping)
          currentStage.value = Math.min(Math.floor(campaignData.CampaignId / 25) + 1, 5);
        }

        if (campaignData.WalkStepCount !== undefined) {
          // Convert steps to CO2 absorption (example: 1 step = 0.1 CO2)
          const stepsToC02 = campaignData.WalkStepCount * 0.1;
          co2Absorbed.value = Math.floor(stepsToC02);

          // Set target based on current stage
          co2Target.value = currentStage.value * 1000;

          // Calculate total mitigated
          totalCO2Mitigated.value = Math.floor(stepsToC02 * 1.2);

          // Calculate items recycled (example: every 100 steps = 1 item)
          totalItemsRecycled.value = Math.floor(campaignData.WalkStepCount / 100);

          // Calculate trees grown based on stage
          totalTreesGrown.value = Math.max(1, currentStage.value);
        }

        console.log('Forest data updated from campaign API:', {
          campaignId: campaignData.CampaignId,
          walkStepCount: campaignData.WalkStepCount,
          description: campaignData.Descn,
          stage: currentStage.value,
          co2Current: co2Absorbed.value,
          co2Total: co2Target.value,
          co2Reduce: totalCO2Mitigated.value,
          itemsRecycled: totalItemsRecycled.value,
          treesGrown: totalTreesGrown.value,
          progressPercentage: progressPercentage.value,
        });
      } else {
        console.warn('No Table0 data found in API response');
      }
    } else {
      console.warn('No forest data found in API response');
    }
  } catch (error) {
    console.error('Error fetching forest statistics:', error);
    // Fallback to existing mock data if API fails
  }
};

// Initialize location and weather data
const initializeForestEnvironment = async () => {
  try {
    // Get user's location
    const position = await initializeLocation();

    // Get weather data based on location
    if (position) {
      const weather = await initializeWeather();
      if (weather) {
        isWeatherLoaded.value = true;
        console.log('Weather loaded:', weather);
      }
    }
  } catch (error) {
    console.error('Error initializing forest environment:', error);
  }
};

onMounted(async () => {
  console.log('Forest view mounted');

  // Initialize location and weather
  initializeForestEnvironment();

  // Fetch real forest data from API
  await fetchForestStats();
});
</script>

<template>
  <div class="forest-page" :class="{ 'dark-mode': isDarkMode }">


    <!-- Current Tree Section -->
    <div class="current-tree-section">
      <div class="forest-header">
        <h2 class="section-title">{{ t('forest.currentTree', 'Current tree') }}</h2>

        <!-- Weather information and controls -->
        <div class="weather-container">
          <div v-if="isWeatherLoaded" class="weather-info">
            <div class="weather-emoji">{{ weatherEmoji }}</div>
            <div class="weather-details">
              <p class="weather-temp">{{ temperature }}°C</p>
              <p class="weather-desc">{{ weatherDescription }}</p>
            </div>
          </div>
          <div class="control-buttons">
            <button class="refresh-weather-btn" :disabled="weatherLoading" title="Refresh Weather" @click="initializeWeather">
              <span v-if="!weatherLoading">🌤️</span>
              <span v-else>⏳</span>
            </button>
            <button class="refresh-forest-btn" :disabled="forestLoading" title="Refresh Forest Data" @click="fetchForestStats">
              <span v-if="!forestLoading">🌳</span>
              <span v-else>⏳</span>
            </button>
          </div>
        </div>
      </div>

      <p class="tree-stage">{{ t('forest.stage', 'Stage') }}: {{ currentStage }}</p>

      <!-- Loading and error states -->
      <div v-if="forestLoading" class="loading-state">
        <p>🌱 {{ t('forest.loading', 'Loading forest data...') }}</p>
      </div>

      <div v-if="forestError && !forestLoading" class="error-state">
        <p>⚠️ {{ t('forest.error', 'Error loading forest data. Using cached data.') }}</p>
      </div>

      <!-- Last update time -->
      <div v-if="lastFetchTime && !forestLoading" class="last-update">
        <p class="update-time">{{ t('forest.lastUpdate', 'Last updated') }}: {{ lastFetchTime.toLocaleTimeString() }}</p>
      </div>

      <!-- Campaign Message -->
      <div v-if="forestApiData && forestApiData.data && forestApiData.data[0]?.Table0 && forestApiData.data[0].Table0[0]?.Descn" class="rvm-message">
        <p class="message-text">{{ forestApiData.data[0].Table0[0].Descn }}</p>
      </div>

      <!-- Tree Visualization -->
      <div class="tree-visualization" :class="{ 'rainy': weatherData?.weatherType === 'rainy' }">
        <!-- Weather effects -->
        <div v-if="weatherData" class="weather-effects" :class="weatherData.weatherType">
          <!-- Rain drops for rainy weather -->
          <div v-if="weatherData.weatherType === 'rainy'" class="rain-container">
            <div
              v-for="n in 40"
              :key="n"
              class="raindrop"
              :style="{
                left: `${Math.random() * 100}%`,
                animationDuration: `${0.7 + Math.random() * 0.9}s`,
                animationDelay: `${Math.random() * 3}s`,
                opacity: `${0.3 + Math.random() * 0.4}`
              }"
            ></div>
          </div>

          <!-- Sun for sunny weather -->
          <div v-if="weatherData.weatherType === 'sunny'" class="sun-container">
            <div class="sun-emoji">{{ weatherEmoji }}</div>
          </div>

          <!-- Cloud overlay for cloudy weather -->
          <div v-if="weatherData.weatherType === 'cloudy'" class="cloud-overlay"></div>
        </div>

        <!-- Cloud decorations - only show for cloudy or rainy weather -->
        <template v-if="weatherData && (weatherData.weatherType === 'cloudy' || weatherData.weatherType === 'rainy')">
          <img
            :src="cloudImage"
            alt="Cloud"
            class="cloud cloud-left"
            :class="{
              'dark-cloud': weatherData.weatherType === 'rainy',
              'prominent-cloud': weatherData.weatherType === 'cloudy'
            }"
          />
          <img
            :src="cloudImage"
            alt="Cloud"
            class="cloud cloud-right"
            :class="{
              'dark-cloud': weatherData.weatherType === 'rainy',
              'prominent-cloud': weatherData.weatherType === 'cloudy'
            }"
          />
          <img
            :src="smallCloudImage"
            alt="Small Cloud"
            class="cloud cloud-small-left"
            :class="{
              'dark-cloud': weatherData.weatherType === 'rainy',
              'prominent-cloud': weatherData.weatherType === 'cloudy'
            }"
          />
          <img
            :src="smallCloudImage"
            alt="Small Cloud"
            class="cloud cloud-small-right"
            :class="{
              'dark-cloud': weatherData.weatherType === 'rainy',
              'prominent-cloud': weatherData.weatherType === 'cloudy'
            }"
          />
        </template>

        <!-- Tree images based on stage and progress -->
        <div class="forest-container">
          <div v-for="(tree, index) in treesToShow" :key="index" :class="['tree-container', `tree-${tree.position}`]">
            <img :src="tree.image" alt="Tree" class="tree-image" />
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
        </div>
        <p class="progress-text">{{ co2Absorbed }}/{{ co2Target }} CO2e absorbed</p>
      </div>

      <!-- Statistics -->
      <div class="statistics-container">
        <div class="stat-item">
          <p class="stat-label">{{ t('forest.totalCO2', 'Total CO2e mitigated') }}</p>
          <p class="stat-value">{{ totalCO2Mitigated }}</p>
        </div>
        <div class="stat-item">
          <p class="stat-label">{{ t('forest.totalItems', 'Total items recycled') }}</p>
          <p class="stat-value">{{ totalItemsRecycled }}</p>
        </div>
        <div class="stat-item">
          <p class="stat-label">{{ t('forest.totalTrees', 'Total trees grown') }}</p>
          <p class="stat-value">{{ totalTreesGrown }}</p>
        </div>
      </div>


    </div>
  </div>
</template>

<style scoped>
.forest-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  color: var(--text-color);
  padding-bottom: 80px; /* Space for bottom navigation */
}

.forest-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #1E88E5; /* Blue color for back button */
  font-size: 1rem;
  cursor: pointer;
  padding: 0;
}

.back-icon {
  margin-right: 0.25rem;
}

.forest-title {
  font-size: 1.2rem;
  margin: 0;
  color: #4CAF50; /* Green color for forest title */
  font-weight: 500;
}

.info-icon {
  font-size: 1.2rem;
  color: var(--text-secondary);
}

.current-tree-section {
  padding: 1rem;
}

.section-title {
  font-size: 1.5rem;
  margin: 0 0 0.5rem 0;
  color: #4CAF50; /* Green color for section title */
}

.tree-stage {
  font-size: 1rem;
  margin: 0 0 1.5rem 0;
  color: var(--text-secondary);
}

.tree-visualization {
  position: relative;
  height: 250px;
  margin: 1rem 0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(to bottom, #87CEEB 0%, #E0F7FF 100%);
}

.dark-mode .tree-visualization {
  background: linear-gradient(to bottom, #1a1a2e 0%, #16213e 100%);
}

.cloud {
  position: absolute;
  animation-name: float;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

.cloud-left {
  width: 120px;
  top: 0;
  left: 0;
  animation-duration: 8s;
}

.cloud-right {
  width: 120px;
  top: 10px;
  right: 0;
  animation-duration: 10s;
  animation-delay: 1s;
  transform: scaleX(-1); /* Flip horizontally for variety */
}

.cloud-small-left {
  width: 80px;
  top: 40px;
  left: 30px;
  animation-duration: 6s;
  animation-delay: 2s;
}

.cloud-small-right {
  width: 80px;
  top: 50px;
  right: 40px;
  animation-duration: 7s;
  animation-delay: 3s;
  transform: scaleX(-1); /* Flip horizontally for variety */
}

@keyframes float {
  0% {
    transform: translateX(0) translateY(0);
  }
  50% {
    transform: translateX(10px) translateY(-5px);
  }
  100% {
    transform: translateX(-5px) translateY(5px);
  }
}

.forest-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
}

.tree-container {
  width: 120px;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 0;
  transition: all 0.5s ease;
}

.tree-far-left {
  left: 0;
  transform: scale(0.7);
  z-index: 1;
}

.tree-left {
  left: 20%;
  transform: scale(0.8);
  z-index: 2;
}

.tree-center {
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.tree-right {
  right: 20%;
  transform: scale(0.8);
  z-index: 2;
}

.tree-image {
  max-width: 100%;
  max-height: 100%;
  animation: sway 5s ease-in-out infinite alternate;
  transform-origin: bottom center;
}

@keyframes sway {
  0% {
    transform: rotate(-2deg);
  }
  100% {
    transform: rotate(2deg);
  }
}

.progress-container {
  margin: 1rem 0;
}

.progress-bar {
  height: 20px;
  background-color: #E0E0E0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50; /* Green for progress */
  border-radius: 10px;
}

.progress-text {
  text-align: center;
  margin-top: 0.5rem;
  color: var(--text-secondary);
}

.statistics-container {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 0.5rem;
}

.stat-label {
  font-size: 0.8rem;
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 1.2rem;
  margin: 0;
  font-weight: 500;
}

/* Dark mode specific styles */
.dark-mode .cloud {
  filter: brightness(0.8) drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

.dark-mode .progress-fill {
  background-color: #66BB6A; /* Slightly lighter green for better visibility in dark mode */
}

.dark-mode .tree-image {
  filter: brightness(1.1); /* Slightly brighter tree in dark mode for better visibility */
}



/* Weather styles */
.forest-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.weather-container {
  display: flex;
  align-items: center;
}

.weather-info {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-right: 0.5rem;
}

.refresh-weather-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  transition: all 0.2s;
}

.refresh-weather-btn:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.refresh-weather-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.weather-emoji {
  font-size: 2rem;
  margin-right: 0.5rem;
  line-height: 1;
  text-align: center;
  min-width: 40px;
}

.weather-details {
  display: flex;
  flex-direction: column;
}

.weather-temp {
  font-size: 1.1rem;
  font-weight: bold;
  margin: 0;
}

.weather-desc {
  font-size: 0.8rem;
  margin: 0;
  color: var(--text-secondary);
}

/* Weather effects */
.weather-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* Rain effect */
.rain-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.rain-emoji {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 3.5rem;
  filter: drop-shadow(0 0 5px rgba(100, 149, 237, 0.7));
  z-index: 5;
}

.raindrop {
  position: absolute;
  top: -20px;
  width: 1.5px;
  height: 15px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(100, 149, 237, 0.7));
  border-radius: 0 0 2px 2px;
  opacity: 0.6;
  animation: rain linear infinite;
  transform: rotate(10deg);
}

@keyframes rain {
  0% {
    transform: translateY(-20px) rotate(10deg);
  }
  85% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(250px) rotate(10deg);
    opacity: 0;
  }
}

.dark-cloud {
  filter: brightness(0.7) contrast(1.2);
}

.prominent-cloud {
  filter: brightness(0.95) contrast(1.05);
  opacity: 0.95;
  transform-origin: center;
  animation: cloud-pulse 8s ease-in-out infinite;
  z-index: 10;
}

.dark-mode .prominent-cloud {
  filter: brightness(0.7) contrast(1.1);
}

@keyframes cloud-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Sun effect */
.sun-container {
  position: absolute;
  top: 30px;
  left: 80px;
  animation: float-sun 10s ease-in-out infinite;
  z-index: 5;
}

.sun-emoji {
  font-size: 3.5rem;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.7));
  animation: sun-pulse 4s ease-in-out infinite;
}

@keyframes sun-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes float-sun {
  0% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
  100% { transform: translateY(0); }
}

/* Cloud overlay for cloudy weather */
.cloud-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* Override background for cloudy weather */
.weather-effects.cloudy + .forest-container {
  position: relative;
  z-index: 2;
}

.weather-effects.cloudy ~ .cloud-left,
.weather-effects.cloudy ~ .cloud-right,
.weather-effects.cloudy ~ .cloud-small-left,
.weather-effects.cloudy ~ .cloud-small-right {
  z-index: 3;
}

/* Cloudy background */
.tree-visualization:has(.weather-effects.cloudy) {
  background: linear-gradient(to bottom, #a8b0bc 0%, #cbd5e1 100%);
}

.dark-mode .tree-visualization:has(.weather-effects.cloudy) {
  background: linear-gradient(to bottom, #334155 0%, #475569 100%);
}

/* Rainy background effect - more subtle */
.tree-visualization.rainy {
  background: linear-gradient(to bottom, #6b7280 0%, #9ca3af 100%);
}

.dark-mode .tree-visualization.rainy {
  background: linear-gradient(to bottom, #1e293b 0%, #334155 100%);
}

.dark-mode .weather-info {
  background-color: rgba(30, 30, 30, 0.4);
}

.dark-mode .refresh-weather-btn {
  background-color: rgba(30, 30, 30, 0.4);
  color: #fff;
}

.dark-mode .refresh-weather-btn:hover {
  background-color: rgba(50, 50, 50, 0.6);
}

/* Control buttons container */
.control-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Forest refresh button */
.refresh-forest-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(76, 175, 80, 0.2);
  transition: all 0.2s;
}

.refresh-forest-btn:hover {
  background-color: rgba(76, 175, 80, 0.4);
}

.refresh-forest-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dark-mode .refresh-forest-btn {
  background-color: rgba(76, 175, 80, 0.3);
  color: #fff;
}

.dark-mode .refresh-forest-btn:hover {
  background-color: rgba(76, 175, 80, 0.5);
}

/* Loading and error states */
.loading-state {
  text-align: center;
  padding: 0.5rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 8px;
  margin: 0.5rem 0;
}

.loading-state p {
  margin: 0;
  color: #4CAF50;
  font-weight: 500;
}

.error-state {
  text-align: center;
  padding: 0.5rem;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8px;
  margin: 0.5rem 0;
}

.error-state p {
  margin: 0;
  color: #FF9800;
  font-weight: 500;
}

.last-update {
  text-align: center;
  margin: 0.5rem 0;
}

.update-time {
  margin: 0;
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-style: italic;
}

.dark-mode .loading-state {
  background-color: rgba(76, 175, 80, 0.2);
}

.dark-mode .error-state {
  background-color: rgba(255, 152, 0, 0.2);
}

/* RVM Message styles */
.rvm-message {
  text-align: center;
  margin: 1rem 0;
  padding: 1rem;
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 12px;
  border-left: 4px solid #4CAF50;
}

.message-text {
  margin: 0;
  font-size: 1rem;
  color: #4CAF50;
  font-weight: 500;
  line-height: 1.4;
}

.dark-mode .rvm-message {
  background-color: rgba(76, 175, 80, 0.15);
  border-left-color: #66BB6A;
}

.dark-mode .message-text {
  color: #66BB6A;
}


</style>
